import { TrendingUp, TrendingDown, Calendar, Globe } from 'lucide-react';

export default function MarketNewsOverview() {
  const newsItems = [
    {
      id: 1,
      title: 'Global Markets Show Strong Recovery',
      summary: 'Major indices across the world are showing positive momentum as investors regain confidence.',
      category: 'Global Markets',
      time: '2 hours ago',
      trend: 'up',
      impact: 'high'
    },
    {
      id: 2,
      title: 'Tech Stocks Lead Market Rally',
      summary: 'Technology sector continues to outperform with significant gains in major tech companies.',
      category: 'Technology',
      time: '4 hours ago',
      trend: 'up',
      impact: 'medium'
    },
    {
      id: 3,
      title: 'Energy Prices Decline Amid Supply Concerns',
      summary: 'Oil and gas prices drop as supply chain issues begin to stabilize globally.',
      category: 'Energy',
      time: '6 hours ago',
      trend: 'down',
      impact: 'medium'
    },
    {
      id: 4,
      title: 'Federal Reserve Maintains Interest Rates',
      summary: 'Central bank keeps rates steady, signaling cautious approach to economic recovery.',
      category: 'Monetary Policy',
      time: '1 day ago',
      trend: 'neutral',
      impact: 'high'
    }
  ];

  const marketSummary = [
    { name: 'S&P 500', value: '4,567.89', change: '+1.2%', trend: 'up' },
    { name: 'NASDAQ', value: '14,234.56', change: '+2.1%', trend: 'up' },
    { name: 'DOW JONES', value: '34,567.12', change: '+0.8%', trend: 'up' },
    { name: 'FTSE 100', value: '7,234.45', change: '-0.3%', trend: 'down' }
  ];

  return (
    <div className="p-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-white mb-2">Market News Overview</h1>
        <p className="text-gray-400">Stay updated with the latest market trends and financial news.</p>
      </div>

      {/* Market Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {marketSummary.map((market) => (
          <div key={market.name} className="bg-gray-800 border border-gray-700 rounded-lg p-6">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium text-gray-400">{market.name}</h3>
              {market.trend === 'up' ? (
                <TrendingUp className="w-4 h-4 text-green-500" />
              ) : (
                <TrendingDown className="w-4 h-4 text-red-500" />
              )}
            </div>
            <p className="text-xl font-bold text-white">{market.value}</p>
            <p className={`text-sm font-medium ${
              market.trend === 'up' ? 'text-green-500' : 'text-red-500'
            }`}>
              {market.change}
            </p>
          </div>
        ))}
      </div>

      {/* News Feed */}
      <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-white">Latest News</h2>
          <div className="flex items-center space-x-2 text-sm text-gray-400">
            <Calendar className="w-4 h-4" />
            <span>Last updated: {new Date().toLocaleTimeString()}</span>
          </div>
        </div>

        <div className="space-y-6">
          {newsItems.map((news) => (
            <div key={news.id} className="border-b border-gray-700 pb-6 last:border-b-0 last:pb-0">
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      news.impact === 'high' ? 'bg-red-600/20 text-red-400' :
                      news.impact === 'medium' ? 'bg-yellow-600/20 text-yellow-400' :
                      'bg-green-600/20 text-green-400'
                    }`}>
                      {news.impact.toUpperCase()} IMPACT
                    </span>
                    <span className="text-xs text-gray-400">{news.category}</span>
                  </div>
                  <h3 className="text-lg font-semibold text-white mb-2">{news.title}</h3>
                  <p className="text-gray-400 text-sm mb-3">{news.summary}</p>
                  <div className="flex items-center space-x-4 text-xs text-gray-500">
                    <span>{news.time}</span>
                    <div className="flex items-center space-x-1">
                      <Globe className="w-3 h-3" />
                      <span>Global</span>
                    </div>
                  </div>
                </div>
                <div className="ml-4">
                  {news.trend === 'up' ? (
                    <div className="w-8 h-8 bg-green-600/20 rounded-lg flex items-center justify-center">
                      <TrendingUp className="w-4 h-4 text-green-500" />
                    </div>
                  ) : news.trend === 'down' ? (
                    <div className="w-8 h-8 bg-red-600/20 rounded-lg flex items-center justify-center">
                      <TrendingDown className="w-4 h-4 text-red-500" />
                    </div>
                  ) : (
                    <div className="w-8 h-8 bg-gray-600/20 rounded-lg flex items-center justify-center">
                      <div className="w-2 h-2 bg-gray-500 rounded-full" />
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-6 text-center">
          <button className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
            Load More News
          </button>
        </div>
      </div>
    </div>
  );
}
