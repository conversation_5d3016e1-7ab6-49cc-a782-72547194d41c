(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{347:()=>{},1007:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1099:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,2093,23)),Promise.resolve().then(a.t.bind(a,7735,23)),Promise.resolve().then(a.t.bind(a,347,23)),Promise.resolve().then(a.bind(a,7577))},1976:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},2093:e=>{e.exports={style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}},5747:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("utensils",[["path",{d:"M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2",key:"cjf0a3"}],["path",{d:"M7 2v20",key:"1473qp"}],["path",{d:"M21 15V2a5 5 0 0 0-5 5v6c0 1.1.9 2 2 2h3Zm0 0v7",key:"j28e5"}]])},7434:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},7577:(e,t,a)=>{"use strict";a.d(t,{default:()=>N});var r=a(5155),s=a(2115),i=a(6874),l=a.n(i),n=a(8999),c=a(9946);let h=(0,c.A)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]]);var d=a(7580);let o=(0,c.A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]),m=(0,c.A)("user-check",[["path",{d:"m16 11 2 2 4-4",key:"9rsbq5"}],["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]),y=(0,c.A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]);var x=a(1976),u=a(5747);let p=(0,c.A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);var f=a(7434);let v=(0,c.A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),g=(0,c.A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]),k=(0,c.A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),b=(0,c.A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);var w=a(1007);let j=[{name:"Dashboard",href:"/dashboard",icon:h},{name:"Registrations",href:"/registrations",icon:d.A,children:[{name:"All Registrations",href:"/registrations/all",icon:d.A},{name:"New Registration",href:"/registrations/new",icon:o},{name:"Pending Review",href:"/registrations/pending",icon:m},{name:"Search",href:"/registrations/search",icon:y}]},{name:"Medical Info",href:"/medical",icon:x.A},{name:"Dietary Info",href:"/dietary",icon:u.A},{name:"Analytics",href:"/analytics",icon:p},{name:"Reports",href:"/reports",icon:f.A},{name:"Settings",href:"/settings",icon:v},{name:"Security",href:"/security",icon:g}];function N(){let e=(0,n.usePathname)(),[t,a]=(0,s.useState)(["Registrations"]),i=e=>{a(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])},c=t=>"/"===t?"/"===e:e.startsWith(t),h=e=>t.includes(e);return(0,r.jsxs)("div",{className:"w-64 bg-gray-800 border-r border-gray-700 flex flex-col min-h-screen",children:[(0,r.jsx)("div",{className:"p-6 border-b border-gray-700",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-sm",children:"Y"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-xl font-semibold text-white",children:"Youth System"}),(0,r.jsx)("p",{className:"text-xs text-gray-400",children:"v1.0.0"})]})]})}),(0,r.jsx)("nav",{className:"flex-1 px-4 py-6 space-y-1",children:j.map(e=>(0,r.jsx)("div",{children:e.children?(0,r.jsxs)("div",{children:[(0,r.jsxs)("button",{onClick:()=>i(e.name),className:"w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-lg transition-colors ".concat(c(e.href)?"bg-blue-600 text-white":"text-gray-300 hover:bg-gray-700 hover:text-white"),children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(e.icon,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:e.name})]}),h(e.name)?(0,r.jsx)(k,{className:"w-4 h-4"}):(0,r.jsx)(b,{className:"w-4 h-4"})]}),h(e.name)&&(0,r.jsx)("div",{className:"ml-6 mt-1 space-y-1",children:e.children.map(e=>(0,r.jsxs)(l(),{href:e.href,className:"flex items-center space-x-3 px-3 py-2 text-sm rounded-lg transition-colors ".concat(c(e.href)?"bg-blue-600 text-white":"text-gray-400 hover:bg-gray-700 hover:text-white"),children:[(0,r.jsx)(e.icon,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:e.name})]},e.name))})]}):(0,r.jsxs)(l(),{href:e.href,className:"flex items-center space-x-3 px-3 py-2 text-sm font-medium rounded-lg transition-colors ".concat(c(e.href)?"bg-blue-600 text-white":"text-gray-300 hover:bg-gray-700 hover:text-white"),children:[(0,r.jsx)(e.icon,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:e.name})]})},e.name))}),(0,r.jsx)("div",{className:"p-4 border-t border-gray-700",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-gray-700 transition-colors cursor-pointer",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center",children:(0,r.jsx)(w.A,{className:"w-4 h-4 text-gray-300"})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-white truncate",children:"Admin User"}),(0,r.jsx)("p",{className:"text-xs text-gray-400 truncate",children:"<EMAIL>"})]})]})})]})}},7580:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},7735:e=>{e.exports={style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"},className:"__className_9a8899",variable:"__variable_9a8899"}},9946:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var r=a(2115);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,a)=>a?a.toUpperCase():t.toLowerCase()),l=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},n=function(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return t.filter((e,t,a)=>!!e&&""!==e.trim()&&a.indexOf(e)===t).join(" ").trim()},c=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var h={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,r.forwardRef)((e,t)=>{let{color:a="currentColor",size:s=24,strokeWidth:i=2,absoluteStrokeWidth:l,className:d="",children:o,iconNode:m,...y}=e;return(0,r.createElement)("svg",{ref:t,...h,width:s,height:s,stroke:a,strokeWidth:l?24*Number(i)/Number(s):i,className:n("lucide",d),...!o&&!c(y)&&{"aria-hidden":"true"},...y},[...m.map(e=>{let[t,a]=e;return(0,r.createElement)(t,a)}),...Array.isArray(o)?o:[o]])}),o=(e,t)=>{let a=(0,r.forwardRef)((a,i)=>{let{className:c,...h}=a;return(0,r.createElement)(d,{ref:i,iconNode:t,className:n("lucide-".concat(s(l(e))),"lucide-".concat(e),c),...h})});return a.displayName=l(e),a}}},e=>{var t=t=>e(e.s=t);e.O(0,[360,874,441,684,358],()=>t(1099)),_N_E=e.O()}]);