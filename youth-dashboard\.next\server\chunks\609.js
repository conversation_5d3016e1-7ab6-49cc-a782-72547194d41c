exports.id=609,exports.ids=[609],exports.modules={440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var r=s(1658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},1135:()=>{},1382:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(6373).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},1627:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6444,23)),Promise.resolve().then(s.t.bind(s,6042,23)),Promise.resolve().then(s.t.bind(s,8170,23)),Promise.resolve().then(s.t.bind(s,9477,23)),Promise.resolve().then(s.t.bind(s,9345,23)),Promise.resolve().then(s.t.bind(s,2089,23)),Promise.resolve().then(s.t.bind(s,6577,23)),Promise.resolve().then(s.t.bind(s,1307,23))},1715:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6346,23)),Promise.resolve().then(s.t.bind(s,7924,23)),Promise.resolve().then(s.t.bind(s,5656,23)),Promise.resolve().then(s.t.bind(s,99,23)),Promise.resolve().then(s.t.bind(s,8243,23)),Promise.resolve().then(s.t.bind(s,8827,23)),Promise.resolve().then(s.t.bind(s,2763,23)),Promise.resolve().then(s.t.bind(s,7173,23))},1773:(e,t,s)=>{Promise.resolve().then(s.bind(s,8624))},3148:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(6373).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},4431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d,metadata:()=>c});var r=s(7413),i=s(2376),n=s.n(i),a=s(8726),o=s.n(a);s(1135);var l=s(8624);let c={title:"Youth Registration System",description:"Admin dashboard for managing youth program registrations"};function d({children:e}){return(0,r.jsx)("html",{lang:"en",className:"dark",children:(0,r.jsx)("body",{className:`${n().variable} ${o().variable} antialiased bg-gray-900 text-white`,children:(0,r.jsxs)("div",{className:"flex h-screen",children:[(0,r.jsx)(l.default,{}),(0,r.jsx)("main",{className:"flex-1 overflow-auto",children:e})]})})})}},4702:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(6373).A)("user-check",[["path",{d:"m16 11 2 2 4-4",key:"9rsbq5"}],["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},5741:(e,t,s)=>{Promise.resolve().then(s.bind(s,6773))},6373:(e,t,s)=>{"use strict";s.d(t,{A:()=>h});var r=s(1120);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),a=e=>{let t=n(e);return t.charAt(0).toUpperCase()+t.slice(1)},o=(...e)=>e.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim(),l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:s=2,absoluteStrokeWidth:i,className:n="",children:a,iconNode:d,...h},m)=>(0,r.createElement)("svg",{ref:m,...c,width:t,height:t,stroke:e,strokeWidth:i?24*Number(s)/Number(t):s,className:o("lucide",n),...!a&&!l(h)&&{"aria-hidden":"true"},...h},[...d.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(a)?a:[a]])),h=(e,t)=>{let s=(0,r.forwardRef)(({className:s,...n},l)=>(0,r.createElement)(d,{ref:l,iconNode:t,className:o(`lucide-${i(a(e))}`,`lucide-${e}`,s),...n}));return s.displayName=a(e),s}},6773:(e,t,s)=>{"use strict";s.d(t,{default:()=>N});var r=s(687),i=s(3210),n=s(5814),a=s.n(n),o=s(6189),l=s(9625),c=s(1312),d=s(3026),h=s(3508),m=s(9270),x=s(7760),u=s(4577),f=s(3411),p=s(22),y=s(4027),g=s(9891),v=s(8272),b=s(4952),j=s(8869);let w=[{name:"Dashboard",href:"/dashboard",icon:l.A},{name:"Registrations",href:"/registrations",icon:c.A,children:[{name:"All Registrations",href:"/registrations/all",icon:c.A},{name:"New Registration",href:"/registrations/new",icon:d.A},{name:"Pending Review",href:"/registrations/pending",icon:h.A},{name:"Search",href:"/registrations/search",icon:m.A}]},{name:"Medical Info",href:"/medical",icon:x.A},{name:"Dietary Info",href:"/dietary",icon:u.A},{name:"Analytics",href:"/analytics",icon:f.A},{name:"Reports",href:"/reports",icon:p.A},{name:"Settings",href:"/settings",icon:y.A},{name:"Security",href:"/security",icon:g.A}];function N(){let e=(0,o.usePathname)(),[t,s]=(0,i.useState)(["Registrations"]),n=e=>{s(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])},l=t=>"/"===t?"/"===e:e.startsWith(t),c=e=>t.includes(e);return(0,r.jsxs)("div",{className:"w-64 bg-gray-800 border-r border-gray-700 flex flex-col min-h-screen",children:[(0,r.jsx)("div",{className:"p-6 border-b border-gray-700",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-sm",children:"Y"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-xl font-semibold text-white",children:"Youth System"}),(0,r.jsx)("p",{className:"text-xs text-gray-400",children:"v1.0.0"})]})]})}),(0,r.jsx)("nav",{className:"flex-1 px-4 py-6 space-y-1",children:w.map(e=>(0,r.jsx)("div",{children:e.children?(0,r.jsxs)("div",{children:[(0,r.jsxs)("button",{onClick:()=>n(e.name),className:`w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-lg transition-colors ${l(e.href)?"bg-blue-600 text-white":"text-gray-300 hover:bg-gray-700 hover:text-white"}`,children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(e.icon,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:e.name})]}),c(e.name)?(0,r.jsx)(v.A,{className:"w-4 h-4"}):(0,r.jsx)(b.A,{className:"w-4 h-4"})]}),c(e.name)&&(0,r.jsx)("div",{className:"ml-6 mt-1 space-y-1",children:e.children.map(e=>(0,r.jsxs)(a(),{href:e.href,className:`flex items-center space-x-3 px-3 py-2 text-sm rounded-lg transition-colors ${l(e.href)?"bg-blue-600 text-white":"text-gray-400 hover:bg-gray-700 hover:text-white"}`,children:[(0,r.jsx)(e.icon,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:e.name})]},e.name))})]}):(0,r.jsxs)(a(),{href:e.href,className:`flex items-center space-x-3 px-3 py-2 text-sm font-medium rounded-lg transition-colors ${l(e.href)?"bg-blue-600 text-white":"text-gray-300 hover:bg-gray-700 hover:text-white"}`,children:[(0,r.jsx)(e.icon,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:e.name})]})},e.name))}),(0,r.jsx)("div",{className:"p-4 border-t border-gray-700",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-gray-700 transition-colors cursor-pointer",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center",children:(0,r.jsx)(j.A,{className:"w-4 h-4 text-gray-300"})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-white truncate",children:"Admin User"}),(0,r.jsx)("p",{className:"text-xs text-gray-400 truncate",children:"<EMAIL>"})]})]})})]})}},8624:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\youth system\\\\youth-dashboard\\\\src\\\\components\\\\Sidebar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\youth system\\youth-dashboard\\src\\components\\Sidebar.tsx","default")}};