import { 
  Utensils, 
  AlertTriangle, 
  <PERSON>, 
  Apple,
  Search,
  Download,
  Eye,
  Edit,
  <PERSON><PERSON><PERSON>
} from 'lucide-react';

interface DietaryRecord {
  id: number;
  participantName: string;
  age: number;
  dietaryRestrictions: string;
  foodAllergies: string;
  severity: 'low' | 'medium' | 'high';
  specialMeals: boolean;
  notes: string;
  emergencyContact: string;
  emergencyPhone: string;
  lastUpdated: string;
}

export default function DietaryInfoPage() {
  // Mock data - in a real app, this would come from an API
  const dietaryRecords: DietaryRecord[] = [
    {
      id: 1,
      participantName: '<PERSON>',
      age: 16,
      dietaryRestrictions: 'Vegetarian',
      foodAllergies: 'None',
      severity: 'low',
      specialMeals: true,
      notes: 'Prefers plant-based protein options',
      emergencyContact: '<PERSON>',
      emergencyPhone: '(*************',
      lastUpdated: '2024-01-15'
    },
    {
      id: 2,
      participantName: '<PERSON>',
      age: 17,
      dietaryRestrictions: 'None',
      foodAllergies: 'Peanuts (severe), Shellfish (moderate)',
      severity: 'high',
      specialMeals: true,
      notes: 'Requires <PERSON><PERSON><PERSON><PERSON> for peanut exposure. No cross-contamination allowed.',
      emergencyContact: '<PERSON>',
      emergencyPhone: '(*************',
      lastUpdated: '2024-01-14'
    },
    {
      id: 3,
      participantName: '<PERSON> Wilson',
      age: 17,
      dietaryRestrictions: 'Dairy-free, Gluten-free',
      foodAllergies: 'Tree nuts (severe), Dairy (moderate)',
      severity: 'high',
      specialMeals: true,
      notes: 'Celiac disease and severe nut allergy. Requires dedicated prep area.',
      emergencyContact: 'Karen Wilson',
      emergencyPhone: '(*************',
      lastUpdated: '2024-01-11'
    },
    {
      id: 4,
      participantName: 'Michael Brown',
      age: 16,
      dietaryRestrictions: 'Diabetic diet',
      foodAllergies: 'None',
      severity: 'medium',
      specialMeals: true,
      notes: 'Low sugar, controlled carbohydrates. Regular meal timing important.',
      emergencyContact: 'Robert Brown',
      emergencyPhone: '(*************',
      lastUpdated: '2024-01-12'
    },
    {
      id: 5,
      participantName: 'Emma Davis',
      age: 15,
      dietaryRestrictions: 'None',
      foodAllergies: 'None',
      severity: 'low',
      specialMeals: false,
      notes: 'No dietary restrictions or allergies',
      emergencyContact: 'Lisa Davis',
      emergencyPhone: '(*************',
      lastUpdated: '2024-01-13'
    },
    {
      id: 6,
      participantName: 'Ryan Martinez',
      age: 15,
      dietaryRestrictions: 'Halal',
      foodAllergies: 'None',
      severity: 'low',
      specialMeals: true,
      notes: 'Requires halal-certified meat and no pork products',
      emergencyContact: 'Carlos Martinez',
      emergencyPhone: '(*************',
      lastUpdated: '2024-01-10'
    }
  ];

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'high':
        return <AlertTriangle className="w-4 h-4 text-red-600" />;
      case 'medium':
        return <AlertTriangle className="w-4 h-4 text-yellow-600" />;
      case 'low':
        return <Apple className="w-4 h-4 text-green-600" />;
      default:
        return <Apple className="w-4 h-4 text-gray-600" />;
    }
  };

  // Calculate statistics
  const totalRecords = dietaryRecords.length;
  const specialMealsCount = dietaryRecords.filter(record => record.specialMeals).length;
  const highRiskCount = dietaryRecords.filter(record => record.severity === 'high').length;
  const allergiesCount = dietaryRecords.filter(record => record.foodAllergies !== 'None').length;

  // Dietary restriction breakdown
  const restrictionTypes = dietaryRecords.reduce((acc, record) => {
    if (record.dietaryRestrictions !== 'None') {
      const restrictions = record.dietaryRestrictions.split(',').map(r => r.trim());
      restrictions.forEach(restriction => {
        acc[restriction] = (acc[restriction] || 0) + 1;
      });
    }
    return acc;
  }, {} as Record<string, number>);

  return (
    <div className="p-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-white mb-2">Dietary Information</h1>
        <p className="text-gray-400">Manage dietary restrictions and food allergies for all participants</p>
      </div>

      {/* Dietary Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-400">Total Participants</p>
              <p className="text-2xl font-bold text-white">{totalRecords}</p>
            </div>
            <Utensils className="w-8 h-8 text-blue-600" />
          </div>
        </div>
        
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-400">Special Meals</p>
              <p className="text-2xl font-bold text-orange-400">{specialMealsCount}</p>
            </div>
            <Leaf className="w-8 h-8 text-orange-600" />
          </div>
        </div>
        
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-400">High Risk</p>
              <p className="text-2xl font-bold text-red-400">{highRiskCount}</p>
            </div>
            <AlertTriangle className="w-8 h-8 text-red-600" />
          </div>
        </div>
        
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-400">Food Allergies</p>
              <p className="text-2xl font-bold text-yellow-400">{allergiesCount}</p>
            </div>
            <Apple className="w-8 h-8 text-yellow-600" />
          </div>
        </div>
      </div>

      {/* Dietary Restrictions Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
            <PieChart className="w-5 h-5 mr-2" />
            Dietary Restrictions Breakdown
          </h3>
          <div className="space-y-3">
            {Object.entries(restrictionTypes).map(([restriction, count]) => (
              <div key={restriction} className="flex items-center justify-between">
                <span className="text-gray-300">{restriction}</span>
                <span className="text-white font-semibold">{count}</span>
              </div>
            ))}
            {Object.keys(restrictionTypes).length === 0 && (
              <p className="text-gray-400">No dietary restrictions recorded</p>
            )}
          </div>
        </div>

        <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Quick Actions</h3>
          <div className="space-y-3">
            <button className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-500 transition-colors">
              <Download className="w-4 h-4 mr-2" />
              Export Meal Planning Report
            </button>
            <button className="w-full flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-500 transition-colors">
              <Utensils className="w-4 h-4 mr-2" />
              Generate Shopping List
            </button>
            <button className="w-full flex items-center justify-center px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-500 transition-colors">
              <AlertTriangle className="w-4 h-4 mr-2" />
              View High-Risk Allergies
            </button>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-gray-800 border border-gray-700 rounded-lg p-6 mb-8">
        <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
          <div className="flex flex-col md:flex-row gap-4 flex-1">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search by participant name or dietary restriction..."
                className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <select className="px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="">All Risk Levels</option>
              <option value="high">High Risk</option>
              <option value="medium">Medium Risk</option>
              <option value="low">Low Risk</option>
            </select>
            
            <select className="px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="">All Participants</option>
              <option value="special-meals">Special Meals Only</option>
              <option value="allergies">Food Allergies Only</option>
              <option value="restrictions">Dietary Restrictions Only</option>
            </select>
          </div>
        </div>
      </div>

      {/* Dietary Records Table */}
      <div className="bg-gray-800 border border-gray-700 rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-700">
          <h3 className="text-lg font-semibold text-white">Dietary Records</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Participant</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Risk Level</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Dietary Restrictions</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Food Allergies</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Special Meals</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-700">
              {dietaryRecords.map((record) => (
                <tr key={record.id} className="hover:bg-gray-700">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-white">{record.participantName}</div>
                    <div className="text-sm text-gray-400">Age: {record.age}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {getSeverityIcon(record.severity)}
                      <span className={`ml-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full border ${getSeverityColor(record.severity)}`}>
                        {record.severity} risk
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-300 max-w-xs truncate" title={record.dietaryRestrictions}>
                      {record.dietaryRestrictions || 'None'}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-300 max-w-xs truncate" title={record.foodAllergies}>
                      {record.foodAllergies || 'None'}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      record.specialMeals 
                        ? 'bg-orange-100 text-orange-800 border border-orange-200' 
                        : 'bg-gray-100 text-gray-800 border border-gray-200'
                    }`}>
                      {record.specialMeals ? 'Required' : 'Not Required'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button className="text-blue-400 hover:text-blue-300" title="View Full Record">
                        <Eye className="w-4 h-4" />
                      </button>
                      <button className="text-green-400 hover:text-green-300" title="Edit Record">
                        <Edit className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Kitchen Safety Notice */}
      <div className="mt-8 bg-orange-900/20 border border-orange-600 rounded-lg p-6">
        <div className="flex items-start">
          <Utensils className="w-6 h-6 text-orange-400 mt-1" />
          <div className="ml-3">
            <h3 className="text-lg font-semibold text-orange-400 mb-2">Kitchen Safety Guidelines</h3>
            <div className="text-sm text-orange-300 space-y-2">
              <p>• Use separate preparation areas for allergen-free meals</p>
              <p>• Clean all surfaces and utensils between preparing different dietary requirements</p>
              <p>• Label all special meals clearly with participant names and restrictions</p>
              <p>• Keep emergency medications (EpiPens) easily accessible during meal times</p>
              <p>• Train all kitchen staff on cross-contamination prevention</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
