import { Shield, Key, Smartphone, AlertTriangle, CheckCircle, Clock } from 'lucide-react';

export default function Security() {
  const securityEvents = [
    {
      id: 1,
      event: 'Successful login',
      location: 'New York, US',
      device: 'Chrome on Windows',
      time: '2 minutes ago',
      status: 'success'
    },
    {
      id: 2,
      event: 'Password changed',
      location: 'New York, US',
      device: 'Chrome on Windows',
      time: '2 days ago',
      status: 'success'
    },
    {
      id: 3,
      event: 'Failed login attempt',
      location: 'Unknown location',
      device: 'Unknown device',
      time: '1 week ago',
      status: 'warning'
    },
    {
      id: 4,
      event: '2FA enabled',
      location: 'New York, US',
      device: 'Chrome on Windows',
      time: '2 weeks ago',
      status: 'success'
    }
  ];

  return (
    <div className="p-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-white mb-2">Security</h1>
        <p className="text-gray-400">Manage your account security settings and monitor access.</p>
      </div>

      {/* Security Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Security Score</h3>
            <Shield className="w-6 h-6 text-green-500" />
          </div>
          <p className="text-3xl font-bold text-green-400">85%</p>
          <p className="text-sm text-gray-400">Strong security level</p>
        </div>

        <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Active Sessions</h3>
            <Smartphone className="w-6 h-6 text-blue-500" />
          </div>
          <p className="text-3xl font-bold text-blue-400">3</p>
          <p className="text-sm text-gray-400">Devices logged in</p>
        </div>

        <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Last Login</h3>
            <Clock className="w-6 h-6 text-purple-500" />
          </div>
          <p className="text-3xl font-bold text-purple-400">2m</p>
          <p className="text-sm text-gray-400">ago from New York</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Security Settings */}
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
          <h2 className="text-xl font-semibold text-white mb-6">Security Settings</h2>
          
          <div className="space-y-6">
            {/* Password */}
            <div className="flex items-center justify-between p-4 bg-gray-700/50 rounded-lg">
              <div className="flex items-center space-x-3">
                <Key className="w-5 h-5 text-blue-500" />
                <div>
                  <h3 className="text-white font-medium">Password</h3>
                  <p className="text-sm text-gray-400">Last changed 2 days ago</p>
                </div>
              </div>
              <button className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded transition-colors">
                Change
              </button>
            </div>

            {/* Two-Factor Authentication */}
            <div className="flex items-center justify-between p-4 bg-gray-700/50 rounded-lg">
              <div className="flex items-center space-x-3">
                <Smartphone className="w-5 h-5 text-green-500" />
                <div>
                  <h3 className="text-white font-medium">Two-Factor Authentication</h3>
                  <p className="text-sm text-green-400">Enabled</p>
                </div>
              </div>
              <button className="px-3 py-1 bg-gray-600 hover:bg-gray-500 text-white text-sm rounded transition-colors">
                Manage
              </button>
            </div>

            {/* Login Notifications */}
            <div className="flex items-center justify-between p-4 bg-gray-700/50 rounded-lg">
              <div className="flex items-center space-x-3">
                <AlertTriangle className="w-5 h-5 text-yellow-500" />
                <div>
                  <h3 className="text-white font-medium">Login Notifications</h3>
                  <p className="text-sm text-gray-400">Get notified of new logins</p>
                </div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input type="checkbox" defaultChecked className="sr-only peer" />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            {/* Session Timeout */}
            <div className="flex items-center justify-between p-4 bg-gray-700/50 rounded-lg">
              <div className="flex items-center space-x-3">
                <Clock className="w-5 h-5 text-purple-500" />
                <div>
                  <h3 className="text-white font-medium">Session Timeout</h3>
                  <p className="text-sm text-gray-400">Auto logout after 30 minutes</p>
                </div>
              </div>
              <select className="px-3 py-1 bg-gray-600 text-white text-sm rounded border border-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option>15 minutes</option>
                <option selected>30 minutes</option>
                <option>1 hour</option>
                <option>Never</option>
              </select>
            </div>
          </div>
        </div>

        {/* Recent Security Activity */}
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
          <h2 className="text-xl font-semibold text-white mb-6">Recent Security Activity</h2>
          
          <div className="space-y-4">
            {securityEvents.map((event) => (
              <div key={event.id} className="flex items-start space-x-3 p-4 bg-gray-700/50 rounded-lg">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  event.status === 'success' ? 'bg-green-600/20' :
                  event.status === 'warning' ? 'bg-yellow-600/20' :
                  'bg-red-600/20'
                }`}>
                  {event.status === 'success' ? (
                    <CheckCircle className="w-4 h-4 text-green-500" />
                  ) : event.status === 'warning' ? (
                    <AlertTriangle className="w-4 h-4 text-yellow-500" />
                  ) : (
                    <AlertTriangle className="w-4 h-4 text-red-500" />
                  )}
                </div>
                <div className="flex-1">
                  <h3 className="text-white font-medium">{event.event}</h3>
                  <div className="text-sm text-gray-400 space-y-1">
                    <p>{event.location} • {event.device}</p>
                    <p>{event.time}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-6">
            <button className="w-full px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors">
              View All Activity
            </button>
          </div>
        </div>
      </div>

      {/* Emergency Actions */}
      <div className="mt-6 bg-red-900/20 border border-red-700 rounded-lg p-6">
        <h2 className="text-xl font-semibold text-red-400 mb-4">Emergency Actions</h2>
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-white font-medium">Sign out all devices</h3>
            <p className="text-sm text-gray-400">This will sign you out of all devices except this one</p>
          </div>
          <button className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors">
            Sign Out All
          </button>
        </div>
      </div>
    </div>
  );
}
