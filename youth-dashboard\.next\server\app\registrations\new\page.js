(()=>{var e={};e.id=682,e.ids=[682],e.modules={440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});var a=t(1658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1627:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},1715:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},1773:(e,r,t)=>{Promise.resolve().then(t.bind(t,8624))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c,metadata:()=>d});var a=t(7413),s=t(2376),l=t.n(s),n=t(8726),i=t.n(n);t(1135);var o=t(8624);let d={title:"Youth Registration System",description:"Admin dashboard for managing youth program registrations"};function c({children:e}){return(0,a.jsx)("html",{lang:"en",className:"dark",children:(0,a.jsx)("body",{className:`${l().variable} ${i().variable} antialiased bg-gray-900 text-white`,children:(0,a.jsxs)("div",{className:"flex h-screen",children:[(0,a.jsx)(o.default,{}),(0,a.jsx)("main",{className:"flex-1 overflow-auto",children:e})]})})})}},5741:(e,r,t)=>{Promise.resolve().then(t.bind(t,6773))},6468:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>x});var a=t(687),s=t(3210),l=t(8869),n=t(1312),i=t(7760),o=t(4577),d=t(22),c=t(2688);let m=(0,c.A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]),u=(0,c.A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);var g=t(5814),h=t.n(g);function x(){let[e,r]=(0,s.useState)({fullName:"",dateOfBirth:"",gender:"",address:"",city:"",state:"",zipCode:"",phone:"",email:"",emergencyContactName:"",emergencyContactRelationship:"",emergencyContactPhone:"",emergencyContactEmail:"",parentGuardianName:"",parentGuardianPhone:"",parentGuardianEmail:"",parentGuardianRelationship:"",roommateRequest:"",roommateConfirmationNumber:"",medications:"",allergies:"",medicalConditions:"",specialNeeds:"",dietaryRestrictions:"",foodAllergies:"",parentalPermission:!1,parentSignature:"",parentSignatureDate:""}),[t,c]=(0,s.useState)(0),g=[{title:"Personal Information",icon:l.A},{title:"Contact Information",icon:n.A},{title:"Medical Information",icon:i.A},{title:"Dietary Information",icon:o.A},{title:"Parental Permission",icon:d.A}],x=(e,t)=>{r(r=>({...r,[e]:t}))};return(0,a.jsxs)("div",{className:"p-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)(h(),{href:"/registrations",className:"inline-flex items-center text-blue-400 hover:text-blue-300 mb-4",children:[(0,a.jsx)(m,{className:"w-4 h-4 mr-2"}),"Back to Registrations"]}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"New Youth Registration"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Complete the registration form for a new youth participant"})]}),(0,a.jsx)("div",{className:"bg-gray-800 border border-gray-700 rounded-lg p-6 mb-8",children:(0,a.jsx)("div",{className:"flex items-center justify-between",children:g.map((e,r)=>(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:`flex items-center justify-center w-10 h-10 rounded-full border-2 ${r<=t?"bg-blue-600 border-blue-600 text-white":"border-gray-600 text-gray-400"}`,children:(0,a.jsx)(e.icon,{className:"w-5 h-5"})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:`text-sm font-medium ${r<=t?"text-white":"text-gray-400"}`,children:e.title})}),r<g.length-1&&(0,a.jsx)("div",{className:`w-16 h-0.5 ml-6 ${r<t?"bg-blue-600":"bg-gray-600"}`})]},r))})}),(0,a.jsx)("form",{onSubmit:r=>{r.preventDefault(),console.log("Form submitted:",e),alert("Registration submitted successfully!")},children:(0,a.jsxs)("div",{className:"bg-gray-800 border border-gray-700 rounded-lg p-6",children:[0===t&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("h3",{className:"text-xl font-semibold text-white mb-4 flex items-center",children:[(0,a.jsx)(l.A,{className:"w-6 h-6 mr-2"}),"Personal Information"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Full Name *"}),(0,a.jsx)("input",{type:"text",required:!0,value:e.fullName,onChange:e=>x("fullName",e.target.value),className:"w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Enter full legal name"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Date of Birth *"}),(0,a.jsx)("input",{type:"date",required:!0,value:e.dateOfBirth,onChange:e=>x("dateOfBirth",e.target.value),className:"w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Gender *"}),(0,a.jsxs)("select",{required:!0,value:e.gender,onChange:e=>x("gender",e.target.value),className:"w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"",children:"Select gender"}),(0,a.jsx)("option",{value:"male",children:"Male"}),(0,a.jsx)("option",{value:"female",children:"Female"}),(0,a.jsx)("option",{value:"other",children:"Other"}),(0,a.jsx)("option",{value:"prefer-not-to-say",children:"Prefer not to say"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Phone Number *"}),(0,a.jsx)("input",{type:"tel",required:!0,value:e.phone,onChange:e=>x("phone",e.target.value),className:"w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"(*************"})]}),(0,a.jsxs)("div",{className:"md:col-span-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Email Address *"}),(0,a.jsx)("input",{type:"email",required:!0,value:e.email,onChange:e=>x("email",e.target.value),className:"w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"<EMAIL>"})]}),(0,a.jsxs)("div",{className:"md:col-span-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Address *"}),(0,a.jsx)("input",{type:"text",required:!0,value:e.address,onChange:e=>x("address",e.target.value),className:"w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Street address"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"City *"}),(0,a.jsx)("input",{type:"text",required:!0,value:e.city,onChange:e=>x("city",e.target.value),className:"w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"City"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"State *"}),(0,a.jsx)("input",{type:"text",required:!0,value:e.state,onChange:e=>x("state",e.target.value),className:"w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"State"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"ZIP Code *"}),(0,a.jsx)("input",{type:"text",required:!0,value:e.zipCode,onChange:e=>x("zipCode",e.target.value),className:"w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"12345"})]})]})]}),1===t&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("h3",{className:"text-xl font-semibold text-white mb-4 flex items-center",children:[(0,a.jsx)(n.A,{className:"w-6 h-6 mr-2"}),"Contact Information"]}),(0,a.jsxs)("div",{className:"bg-gray-700 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"text-lg font-medium text-white mb-4",children:"Emergency Contact"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Emergency Contact Name *"}),(0,a.jsx)("input",{type:"text",required:!0,value:e.emergencyContactName,onChange:e=>x("emergencyContactName",e.target.value),className:"w-full px-4 py-2 bg-gray-600 border border-gray-500 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Full name"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Relationship *"}),(0,a.jsx)("input",{type:"text",required:!0,value:e.emergencyContactRelationship,onChange:e=>x("emergencyContactRelationship",e.target.value),className:"w-full px-4 py-2 bg-gray-600 border border-gray-500 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"e.g., Mother, Father, Guardian"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Emergency Contact Phone *"}),(0,a.jsx)("input",{type:"tel",required:!0,value:e.emergencyContactPhone,onChange:e=>x("emergencyContactPhone",e.target.value),className:"w-full px-4 py-2 bg-gray-600 border border-gray-500 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"(*************"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Emergency Contact Email"}),(0,a.jsx)("input",{type:"email",value:e.emergencyContactEmail,onChange:e=>x("emergencyContactEmail",e.target.value),className:"w-full px-4 py-2 bg-gray-600 border border-gray-500 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"<EMAIL>"})]})]})]}),(0,a.jsxs)("div",{className:"bg-gray-700 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"text-lg font-medium text-white mb-4",children:"Parent/Guardian Information"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Parent/Guardian Name *"}),(0,a.jsx)("input",{type:"text",required:!0,value:e.parentGuardianName,onChange:e=>x("parentGuardianName",e.target.value),className:"w-full px-4 py-2 bg-gray-600 border border-gray-500 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Full name"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Relationship *"}),(0,a.jsxs)("select",{required:!0,value:e.parentGuardianRelationship,onChange:e=>x("parentGuardianRelationship",e.target.value),className:"w-full px-4 py-2 bg-gray-600 border border-gray-500 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"",children:"Select relationship"}),(0,a.jsx)("option",{value:"mother",children:"Mother"}),(0,a.jsx)("option",{value:"father",children:"Father"}),(0,a.jsx)("option",{value:"guardian",children:"Legal Guardian"}),(0,a.jsx)("option",{value:"other",children:"Other"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Parent/Guardian Phone *"}),(0,a.jsx)("input",{type:"tel",required:!0,value:e.parentGuardianPhone,onChange:e=>x("parentGuardianPhone",e.target.value),className:"w-full px-4 py-2 bg-gray-600 border border-gray-500 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"(*************"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Parent/Guardian Email *"}),(0,a.jsx)("input",{type:"email",required:!0,value:e.parentGuardianEmail,onChange:e=>x("parentGuardianEmail",e.target.value),className:"w-full px-4 py-2 bg-gray-600 border border-gray-500 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"<EMAIL>"})]})]})]}),(0,a.jsxs)("div",{className:"bg-gray-700 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"text-lg font-medium text-white mb-4",children:"Roommate Request (Optional)"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Requested Roommate Name"}),(0,a.jsx)("input",{type:"text",value:e.roommateRequest,onChange:e=>x("roommateRequest",e.target.value),className:"w-full px-4 py-2 bg-gray-600 border border-gray-500 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Friend's full name"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Roommate Confirmation Number"}),(0,a.jsx)("input",{type:"text",value:e.roommateConfirmationNumber,onChange:e=>x("roommateConfirmationNumber",e.target.value),className:"w-full px-4 py-2 bg-gray-600 border border-gray-500 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Confirmation number"})]})]})]})]}),2===t&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("h3",{className:"text-xl font-semibold text-white mb-4 flex items-center",children:[(0,a.jsx)(i.A,{className:"w-6 h-6 mr-2"}),"Medical Information"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Current Medications"}),(0,a.jsx)("textarea",{value:e.medications,onChange:e=>x("medications",e.target.value),rows:3,className:"w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"List all current medications, dosages, and frequency. Write 'None' if no medications."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Allergies"}),(0,a.jsx)("textarea",{value:e.allergies,onChange:e=>x("allergies",e.target.value),rows:3,className:"w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"List all known allergies (medications, environmental, etc.). Write 'None' if no allergies."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Medical Conditions"}),(0,a.jsx)("textarea",{value:e.medicalConditions,onChange:e=>x("medicalConditions",e.target.value),rows:3,className:"w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"List any medical conditions, chronic illnesses, or ongoing health concerns. Write 'None' if no conditions."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Special Needs or Accommodations"}),(0,a.jsx)("textarea",{value:e.specialNeeds,onChange:e=>x("specialNeeds",e.target.value),rows:3,className:"w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Describe any special needs, accommodations, or assistance required. Write 'None' if no special needs."})]})]})]}),3===t&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("h3",{className:"text-xl font-semibold text-white mb-4 flex items-center",children:[(0,a.jsx)(o.A,{className:"w-6 h-6 mr-2"}),"Dietary Information"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Dietary Restrictions"}),(0,a.jsx)("textarea",{value:e.dietaryRestrictions,onChange:e=>x("dietaryRestrictions",e.target.value),rows:3,className:"w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"List any dietary restrictions (vegetarian, vegan, kosher, halal, etc.). Write 'None' if no restrictions."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Food Allergies"}),(0,a.jsx)("textarea",{value:e.foodAllergies,onChange:e=>x("foodAllergies",e.target.value),rows:3,className:"w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"List all food allergies and severity (nuts, dairy, gluten, etc.). Write 'None' if no food allergies."})]})]})]}),4===t&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("h3",{className:"text-xl font-semibold text-white mb-4 flex items-center",children:[(0,a.jsx)(d.A,{className:"w-6 h-6 mr-2"}),"Parental Permission"]}),(0,a.jsx)("div",{className:"bg-yellow-900/20 border border-yellow-600 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"w-5 h-5 text-yellow-400",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-yellow-400",children:"Important Notice"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-yellow-300",children:"For participants under 18 years of age, parental or guardian permission is required."})]})]})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"parentalPermission",checked:e.parentalPermission,onChange:e=>x("parentalPermission",e.target.checked),className:"w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2"}),(0,a.jsx)("label",{htmlFor:"parentalPermission",className:"ml-2 text-sm text-gray-300",children:"I give permission for my child to participate in this youth program and acknowledge that I have read and understood all terms and conditions."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Parent/Guardian Digital Signature *"}),(0,a.jsx)("input",{type:"text",required:!0,value:e.parentSignature,onChange:e=>x("parentSignature",e.target.value),className:"w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Type your full name as digital signature"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Signature Date *"}),(0,a.jsx)("input",{type:"date",required:!0,value:e.parentSignatureDate,onChange:e=>x("parentSignatureDate",e.target.value),className:"w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-between mt-8 pt-6 border-t border-gray-700",children:[(0,a.jsx)("button",{type:"button",onClick:()=>{t>0&&c(t-1)},disabled:0===t,className:"px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),t===g.length-1?(0,a.jsxs)("button",{type:"submit",className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-500 flex items-center",children:[(0,a.jsx)(u,{className:"w-4 h-4 mr-2"}),"Submit Registration"]}):(0,a.jsx)("button",{type:"button",onClick:()=>{t<g.length-1&&c(t+1)},className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-500",children:"Next"})]})]})})]})}},6773:(e,r,t)=>{"use strict";t.d(r,{default:()=>w});var a=t(687),s=t(3210),l=t(5814),n=t.n(l),i=t(6189),o=t(9625),d=t(1312),c=t(3026),m=t(3508),u=t(9270),g=t(7760),h=t(4577),x=t(3411),p=t(22),b=t(4027),y=t(9891),f=t(8272),v=t(4952),j=t(8869);let N=[{name:"Dashboard",href:"/dashboard",icon:o.A},{name:"Registrations",href:"/registrations",icon:d.A,children:[{name:"All Registrations",href:"/registrations/all",icon:d.A},{name:"New Registration",href:"/registrations/new",icon:c.A},{name:"Pending Review",href:"/registrations/pending",icon:m.A},{name:"Search",href:"/registrations/search",icon:u.A}]},{name:"Medical Info",href:"/medical",icon:g.A},{name:"Dietary Info",href:"/dietary",icon:h.A},{name:"Analytics",href:"/analytics",icon:x.A},{name:"Reports",href:"/reports",icon:p.A},{name:"Settings",href:"/settings",icon:b.A},{name:"Security",href:"/security",icon:y.A}];function w(){let e=(0,i.usePathname)(),[r,t]=(0,s.useState)(["Registrations"]),l=e=>{t(r=>r.includes(e)?r.filter(r=>r!==e):[...r,e])},o=r=>"/"===r?"/"===e:e.startsWith(r),d=e=>r.includes(e);return(0,a.jsxs)("div",{className:"w-64 bg-gray-800 border-r border-gray-700 flex flex-col min-h-screen",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-700",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold text-sm",children:"Y"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-xl font-semibold text-white",children:"Youth System"}),(0,a.jsx)("p",{className:"text-xs text-gray-400",children:"v1.0.0"})]})]})}),(0,a.jsx)("nav",{className:"flex-1 px-4 py-6 space-y-1",children:N.map(e=>(0,a.jsx)("div",{children:e.children?(0,a.jsxs)("div",{children:[(0,a.jsxs)("button",{onClick:()=>l(e.name),className:`w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-lg transition-colors ${o(e.href)?"bg-blue-600 text-white":"text-gray-300 hover:bg-gray-700 hover:text-white"}`,children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(e.icon,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:e.name})]}),d(e.name)?(0,a.jsx)(f.A,{className:"w-4 h-4"}):(0,a.jsx)(v.A,{className:"w-4 h-4"})]}),d(e.name)&&(0,a.jsx)("div",{className:"ml-6 mt-1 space-y-1",children:e.children.map(e=>(0,a.jsxs)(n(),{href:e.href,className:`flex items-center space-x-3 px-3 py-2 text-sm rounded-lg transition-colors ${o(e.href)?"bg-blue-600 text-white":"text-gray-400 hover:bg-gray-700 hover:text-white"}`,children:[(0,a.jsx)(e.icon,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:e.name})]},e.name))})]}):(0,a.jsxs)(n(),{href:e.href,className:`flex items-center space-x-3 px-3 py-2 text-sm font-medium rounded-lg transition-colors ${o(e.href)?"bg-blue-600 text-white":"text-gray-300 hover:bg-gray-700 hover:text-white"}`,children:[(0,a.jsx)(e.icon,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:e.name})]})},e.name))}),(0,a.jsx)("div",{className:"p-4 border-t border-gray-700",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-gray-700 transition-colors cursor-pointer",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center",children:(0,a.jsx)(j.A,{className:"w-4 h-4 text-gray-300"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-white truncate",children:"Admin User"}),(0,a.jsx)("p",{className:"text-xs text-gray-400 truncate",children:"<EMAIL>"})]})]})})]})}},7330:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var a=t(5239),s=t(8088),l=t(8170),n=t.n(l),i=t(893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);t.d(r,o);let d={children:["",{children:["registrations",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,8532)),"C:\\Users\\<USER>\\Desktop\\youth system\\youth-dashboard\\src\\app\\registrations\\new\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"C:\\Users\\<USER>\\Desktop\\youth system\\youth-dashboard\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\youth system\\youth-dashboard\\src\\app\\registrations\\new\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/registrations/new/page",pathname:"/registrations/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},8231:(e,r,t)=>{Promise.resolve().then(t.bind(t,6468))},8532:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});let a=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\youth system\\\\youth-dashboard\\\\src\\\\app\\\\registrations\\\\new\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\youth system\\youth-dashboard\\src\\app\\registrations\\new\\page.tsx","default")},8624:(e,r,t)=>{"use strict";t.d(r,{default:()=>a});let a=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\youth system\\\\youth-dashboard\\\\src\\\\components\\\\Sidebar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\youth system\\youth-dashboard\\src\\components\\Sidebar.tsx","default")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9983:(e,r,t)=>{Promise.resolve().then(t.bind(t,8532))}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[447,354,658],()=>t(7330));module.exports=a})();