(()=>{var e={};e.id=105,e.ids=[105],e.modules={370:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>h,tree:()=>o});var r=s(5239),a=s(8088),i=s(8170),n=s.n(i),d=s(893),l={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>d[e]);s.d(t,l);let o={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,5981)),"C:\\Users\\<USER>\\Desktop\\youth system\\youth-dashboard\\src\\app\\dashboard\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"C:\\Users\\<USER>\\Desktop\\youth system\\youth-dashboard\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\youth system\\youth-dashboard\\src\\app\\dashboard\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},5981:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p});var r=s(7413),a=s(1382),i=s(4702),n=s(3148),d=s(6373);let l=(0,d.A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]),o=(0,d.A)("arrow-up-right",[["path",{d:"M7 7h10v10",key:"1tivn9"}],["path",{d:"M7 17 17 7",key:"1vkiza"}]]),c=(0,d.A)("arrow-down-right",[["path",{d:"m7 7 10 10",key:"1fmybs"}],["path",{d:"M17 7v10H7",key:"6fjiku"}]]),m=(0,d.A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]),h=(0,d.A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),x=(0,d.A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]);function p(){let e=[{title:"Total Registrations",value:"1,247",change:"+12.5%",trend:"up",icon:a.A},{title:"Approved",value:"1,156",change:"+8.2%",trend:"up",icon:i.A},{title:"Pending Review",value:"91",change:"+15",trend:"up",icon:n.A},{title:"Medical Alerts",value:"23",change:"+3",trend:"up",icon:l}];return(0,r.jsxs)("div",{className:"p-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"Dashboard"}),(0,r.jsx)("p",{className:"text-gray-400",children:"Welcome back! Here's an overview of your youth registration system."})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:e.map(e=>(0,r.jsxs)("div",{className:"bg-gray-800 border border-gray-700 rounded-lg p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-400",children:e.title}),(0,r.jsx)("p",{className:"text-2xl font-bold text-white",children:e.value})]}),(0,r.jsx)("div",{className:"w-12 h-12 bg-blue-600/10 rounded-lg flex items-center justify-center",children:(0,r.jsx)(e.icon,{className:"w-6 h-6 text-blue-600"})})]}),(0,r.jsxs)("div",{className:"mt-4 flex items-center",children:["up"===e.trend?(0,r.jsx)(o,{className:"w-4 h-4 text-green-500 mr-1"}):(0,r.jsx)(c,{className:"w-4 h-4 text-red-500 mr-1"}),(0,r.jsx)("span",{className:`text-sm font-medium ${"up"===e.trend?"text-green-500":"text-red-500"}`,children:e.change}),(0,r.jsx)("span",{className:"text-sm text-gray-400 ml-1",children:"from last month"})]})]},e.title))}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8",children:[(0,r.jsxs)("div",{className:"bg-gray-800 border border-gray-700 rounded-lg p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Registration Trends"}),(0,r.jsx)(m,{className:"w-5 h-5 text-blue-600"})]}),(0,r.jsx)("div",{className:"h-64 flex items-center justify-center text-gray-400",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(h,{className:"w-16 h-16 mx-auto mb-4 text-gray-600"}),(0,r.jsx)("p",{children:"Registration trends chart would go here"}),(0,r.jsx)("p",{className:"text-sm",children:"Total Registrations: 1,247"})]})})]}),(0,r.jsxs)("div",{className:"bg-gray-800 border border-gray-700 rounded-lg p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Age Distribution"}),(0,r.jsx)(x,{className:"w-5 h-5 text-green-600"})]}),(0,r.jsx)("div",{className:"h-64 flex items-center justify-center text-gray-400",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(x,{className:"w-16 h-16 mx-auto mb-4 text-gray-600"}),(0,r.jsx)("p",{children:"Age distribution chart would go here"}),(0,r.jsx)("p",{className:"text-sm",children:"Average Age: 16.2 years"})]})})]})]}),(0,r.jsxs)("div",{className:"bg-gray-800 border border-gray-700 rounded-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Recent Registrations"}),(0,r.jsx)("div",{className:"space-y-4",children:[{action:"Sarah Johnson registered",time:"2 minutes ago",type:"registration"},{action:"Medical form submitted by Alex Chen",time:"5 minutes ago",type:"medical"},{action:"Emergency contact updated for Emma Davis",time:"10 minutes ago",type:"update"},{action:"Registration approved for Michael Brown",time:"1 hour ago",type:"approval"}].map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center justify-between py-3 border-b border-gray-700 last:border-b-0",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:`w-2 h-2 rounded-full ${"registration"===e.type?"bg-blue-500":"medical"===e.type?"bg-red-500":"update"===e.type?"bg-yellow-500":"bg-green-500"}`}),(0,r.jsx)("span",{className:"text-white",children:e.action})]}),(0,r.jsx)("span",{className:"text-sm text-gray-400",children:e.time})]},t))})]})]})}},6487:()=>{},8335:()=>{},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,354,658,609],()=>s(370));module.exports=r})();