import { BarChart3, TrendingUp, Calendar, Target } from 'lucide-react';

export default function MarketForecasts() {
  const forecasts = [
    {
      id: 1,
      title: 'Q4 2024 Market Outlook',
      prediction: 'Bullish',
      confidence: 85,
      timeframe: '3 months',
      category: 'General Market',
      details: 'Strong earnings growth expected across major sectors with continued consumer spending.',
      analyst: 'Goldman Sachs Research'
    },
    {
      id: 2,
      title: 'Technology Sector Growth',
      prediction: 'Very Bullish',
      confidence: 92,
      timeframe: '6 months',
      category: 'Technology',
      details: 'AI and cloud computing driving unprecedented growth in tech valuations.',
      analyst: 'Morgan Stanley'
    },
    {
      id: 3,
      title: 'Energy Market Stabilization',
      prediction: 'Neutral',
      confidence: 70,
      timeframe: '12 months',
      category: 'Energy',
      details: 'Oil prices expected to stabilize around $75-85 per barrel range.',
      analyst: '<PERSON> Morgan'
    },
    {
      id: 4,
      title: 'Real Estate Correction',
      prediction: 'Bearish',
      confidence: 78,
      timeframe: '9 months',
      category: 'Real Estate',
      details: 'Housing market showing signs of cooling with potential 10-15% price adjustments.',
      analyst: 'Bank of America'
    }
  ];

  const getPredictionColor = (prediction: string) => {
    switch (prediction.toLowerCase()) {
      case 'very bullish':
        return 'text-green-400 bg-green-600/20';
      case 'bullish':
        return 'text-green-300 bg-green-600/10';
      case 'neutral':
        return 'text-yellow-400 bg-yellow-600/20';
      case 'bearish':
        return 'text-red-300 bg-red-600/10';
      case 'very bearish':
        return 'text-red-400 bg-red-600/20';
      default:
        return 'text-gray-400 bg-gray-600/20';
    }
  };

  return (
    <div className="p-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-white mb-2">Market Forecasts</h1>
        <p className="text-gray-400">Expert predictions and analysis for upcoming market trends.</p>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Bullish Forecasts</h3>
            <TrendingUp className="w-6 h-6 text-green-500" />
          </div>
          <p className="text-3xl font-bold text-green-400">67%</p>
          <p className="text-sm text-gray-400">of analysts are optimistic</p>
        </div>

        <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Average Confidence</h3>
            <Target className="w-6 h-6 text-blue-500" />
          </div>
          <p className="text-3xl font-bold text-blue-400">81%</p>
          <p className="text-sm text-gray-400">analyst confidence level</p>
        </div>

        <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Forecast Horizon</h3>
            <Calendar className="w-6 h-6 text-purple-500" />
          </div>
          <p className="text-3xl font-bold text-purple-400">6.8</p>
          <p className="text-sm text-gray-400">months average timeframe</p>
        </div>
      </div>

      {/* Forecasts List */}
      <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-white">Expert Forecasts</h2>
          <div className="flex items-center space-x-2">
            <BarChart3 className="w-5 h-5 text-gray-400" />
            <span className="text-sm text-gray-400">Updated daily</span>
          </div>
        </div>

        <div className="space-y-6">
          {forecasts.map((forecast) => (
            <div key={forecast.id} className="border border-gray-700 rounded-lg p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <span className={`px-3 py-1 text-sm font-medium rounded-full ${getPredictionColor(forecast.prediction)}`}>
                      {forecast.prediction.toUpperCase()}
                    </span>
                    <span className="text-sm text-gray-400">{forecast.category}</span>
                  </div>
                  <h3 className="text-lg font-semibold text-white mb-2">{forecast.title}</h3>
                  <p className="text-gray-400 text-sm mb-3">{forecast.details}</p>
                </div>
                <div className="ml-6 text-right">
                  <div className="text-2xl font-bold text-white mb-1">{forecast.confidence}%</div>
                  <div className="text-xs text-gray-400">Confidence</div>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-6 text-sm text-gray-400">
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-4 h-4" />
                    <span>{forecast.timeframe}</span>
                  </div>
                  <div>
                    <span>By {forecast.analyst}</span>
                  </div>
                </div>
                
                {/* Confidence Bar */}
                <div className="flex items-center space-x-2">
                  <span className="text-xs text-gray-400">Confidence:</span>
                  <div className="w-24 h-2 bg-gray-700 rounded-full overflow-hidden">
                    <div 
                      className={`h-full rounded-full ${
                        forecast.confidence >= 80 ? 'bg-green-500' :
                        forecast.confidence >= 60 ? 'bg-yellow-500' :
                        'bg-red-500'
                      }`}
                      style={{ width: `${forecast.confidence}%` }}
                    />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
