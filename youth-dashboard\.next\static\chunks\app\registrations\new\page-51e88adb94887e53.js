(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[682],{1007:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1976:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(9946).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},5747:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(9946).A)("utensils",[["path",{d:"M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2",key:"cjf0a3"}],["path",{d:"M7 2v20",key:"1473qp"}],["path",{d:"M21 15V2a5 5 0 0 0-5 5v6c0 1.1.9 2 2 2h3Zm0 0v7",key:"j28e5"}]])},5809:(e,a,r)=>{Promise.resolve().then(r.bind(r,6725))},6725:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>x});var t=r(5155),l=r(2115),s=r(1007),n=r(7580),i=r(1976),o=r(5747),d=r(7434),c=r(9946);let u=(0,c.A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]),m=(0,c.A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);var g=r(6874),h=r.n(g);function x(){let[e,a]=(0,l.useState)({fullName:"",dateOfBirth:"",gender:"",address:"",city:"",state:"",zipCode:"",phone:"",email:"",emergencyContactName:"",emergencyContactRelationship:"",emergencyContactPhone:"",emergencyContactEmail:"",parentGuardianName:"",parentGuardianPhone:"",parentGuardianEmail:"",parentGuardianRelationship:"",roommateRequest:"",roommateConfirmationNumber:"",medications:"",allergies:"",medicalConditions:"",specialNeeds:"",dietaryRestrictions:"",foodAllergies:"",parentalPermission:!1,parentSignature:"",parentSignatureDate:""}),[r,c]=(0,l.useState)(0),g=[{title:"Personal Information",icon:s.A},{title:"Contact Information",icon:n.A},{title:"Medical Information",icon:i.A},{title:"Dietary Information",icon:o.A},{title:"Parental Permission",icon:d.A}],x=(e,r)=>{a(a=>({...a,[e]:r}))};return(0,t.jsxs)("div",{className:"p-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsxs)(h(),{href:"/registrations",className:"inline-flex items-center text-blue-400 hover:text-blue-300 mb-4",children:[(0,t.jsx)(u,{className:"w-4 h-4 mr-2"}),"Back to Registrations"]}),(0,t.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"New Youth Registration"}),(0,t.jsx)("p",{className:"text-gray-400",children:"Complete the registration form for a new youth participant"})]}),(0,t.jsx)("div",{className:"bg-gray-800 border border-gray-700 rounded-lg p-6 mb-8",children:(0,t.jsx)("div",{className:"flex items-center justify-between",children:g.map((e,a)=>(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-full border-2 ".concat(a<=r?"bg-blue-600 border-blue-600 text-white":"border-gray-600 text-gray-400"),children:(0,t.jsx)(e.icon,{className:"w-5 h-5"})}),(0,t.jsx)("div",{className:"ml-3",children:(0,t.jsx)("p",{className:"text-sm font-medium ".concat(a<=r?"text-white":"text-gray-400"),children:e.title})}),a<g.length-1&&(0,t.jsx)("div",{className:"w-16 h-0.5 ml-6 ".concat(a<r?"bg-blue-600":"bg-gray-600")})]},a))})}),(0,t.jsx)("form",{onSubmit:a=>{a.preventDefault(),console.log("Form submitted:",e),alert("Registration submitted successfully!")},children:(0,t.jsxs)("div",{className:"bg-gray-800 border border-gray-700 rounded-lg p-6",children:[0===r&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("h3",{className:"text-xl font-semibold text-white mb-4 flex items-center",children:[(0,t.jsx)(s.A,{className:"w-6 h-6 mr-2"}),"Personal Information"]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Full Name *"}),(0,t.jsx)("input",{type:"text",required:!0,value:e.fullName,onChange:e=>x("fullName",e.target.value),className:"w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Enter full legal name"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Date of Birth *"}),(0,t.jsx)("input",{type:"date",required:!0,value:e.dateOfBirth,onChange:e=>x("dateOfBirth",e.target.value),className:"w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Gender *"}),(0,t.jsxs)("select",{required:!0,value:e.gender,onChange:e=>x("gender",e.target.value),className:"w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)("option",{value:"",children:"Select gender"}),(0,t.jsx)("option",{value:"male",children:"Male"}),(0,t.jsx)("option",{value:"female",children:"Female"}),(0,t.jsx)("option",{value:"other",children:"Other"}),(0,t.jsx)("option",{value:"prefer-not-to-say",children:"Prefer not to say"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Phone Number *"}),(0,t.jsx)("input",{type:"tel",required:!0,value:e.phone,onChange:e=>x("phone",e.target.value),className:"w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"(*************"})]}),(0,t.jsxs)("div",{className:"md:col-span-2",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Email Address *"}),(0,t.jsx)("input",{type:"email",required:!0,value:e.email,onChange:e=>x("email",e.target.value),className:"w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"<EMAIL>"})]}),(0,t.jsxs)("div",{className:"md:col-span-2",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Address *"}),(0,t.jsx)("input",{type:"text",required:!0,value:e.address,onChange:e=>x("address",e.target.value),className:"w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Street address"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"City *"}),(0,t.jsx)("input",{type:"text",required:!0,value:e.city,onChange:e=>x("city",e.target.value),className:"w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"City"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"State *"}),(0,t.jsx)("input",{type:"text",required:!0,value:e.state,onChange:e=>x("state",e.target.value),className:"w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"State"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"ZIP Code *"}),(0,t.jsx)("input",{type:"text",required:!0,value:e.zipCode,onChange:e=>x("zipCode",e.target.value),className:"w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"12345"})]})]})]}),1===r&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("h3",{className:"text-xl font-semibold text-white mb-4 flex items-center",children:[(0,t.jsx)(n.A,{className:"w-6 h-6 mr-2"}),"Contact Information"]}),(0,t.jsxs)("div",{className:"bg-gray-700 rounded-lg p-4",children:[(0,t.jsx)("h4",{className:"text-lg font-medium text-white mb-4",children:"Emergency Contact"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Emergency Contact Name *"}),(0,t.jsx)("input",{type:"text",required:!0,value:e.emergencyContactName,onChange:e=>x("emergencyContactName",e.target.value),className:"w-full px-4 py-2 bg-gray-600 border border-gray-500 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Full name"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Relationship *"}),(0,t.jsx)("input",{type:"text",required:!0,value:e.emergencyContactRelationship,onChange:e=>x("emergencyContactRelationship",e.target.value),className:"w-full px-4 py-2 bg-gray-600 border border-gray-500 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"e.g., Mother, Father, Guardian"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Emergency Contact Phone *"}),(0,t.jsx)("input",{type:"tel",required:!0,value:e.emergencyContactPhone,onChange:e=>x("emergencyContactPhone",e.target.value),className:"w-full px-4 py-2 bg-gray-600 border border-gray-500 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"(*************"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Emergency Contact Email"}),(0,t.jsx)("input",{type:"email",value:e.emergencyContactEmail,onChange:e=>x("emergencyContactEmail",e.target.value),className:"w-full px-4 py-2 bg-gray-600 border border-gray-500 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"<EMAIL>"})]})]})]}),(0,t.jsxs)("div",{className:"bg-gray-700 rounded-lg p-4",children:[(0,t.jsx)("h4",{className:"text-lg font-medium text-white mb-4",children:"Parent/Guardian Information"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Parent/Guardian Name *"}),(0,t.jsx)("input",{type:"text",required:!0,value:e.parentGuardianName,onChange:e=>x("parentGuardianName",e.target.value),className:"w-full px-4 py-2 bg-gray-600 border border-gray-500 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Full name"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Relationship *"}),(0,t.jsxs)("select",{required:!0,value:e.parentGuardianRelationship,onChange:e=>x("parentGuardianRelationship",e.target.value),className:"w-full px-4 py-2 bg-gray-600 border border-gray-500 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)("option",{value:"",children:"Select relationship"}),(0,t.jsx)("option",{value:"mother",children:"Mother"}),(0,t.jsx)("option",{value:"father",children:"Father"}),(0,t.jsx)("option",{value:"guardian",children:"Legal Guardian"}),(0,t.jsx)("option",{value:"other",children:"Other"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Parent/Guardian Phone *"}),(0,t.jsx)("input",{type:"tel",required:!0,value:e.parentGuardianPhone,onChange:e=>x("parentGuardianPhone",e.target.value),className:"w-full px-4 py-2 bg-gray-600 border border-gray-500 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"(*************"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Parent/Guardian Email *"}),(0,t.jsx)("input",{type:"email",required:!0,value:e.parentGuardianEmail,onChange:e=>x("parentGuardianEmail",e.target.value),className:"w-full px-4 py-2 bg-gray-600 border border-gray-500 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"<EMAIL>"})]})]})]}),(0,t.jsxs)("div",{className:"bg-gray-700 rounded-lg p-4",children:[(0,t.jsx)("h4",{className:"text-lg font-medium text-white mb-4",children:"Roommate Request (Optional)"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Requested Roommate Name"}),(0,t.jsx)("input",{type:"text",value:e.roommateRequest,onChange:e=>x("roommateRequest",e.target.value),className:"w-full px-4 py-2 bg-gray-600 border border-gray-500 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Friend's full name"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Roommate Confirmation Number"}),(0,t.jsx)("input",{type:"text",value:e.roommateConfirmationNumber,onChange:e=>x("roommateConfirmationNumber",e.target.value),className:"w-full px-4 py-2 bg-gray-600 border border-gray-500 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Confirmation number"})]})]})]})]}),2===r&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("h3",{className:"text-xl font-semibold text-white mb-4 flex items-center",children:[(0,t.jsx)(i.A,{className:"w-6 h-6 mr-2"}),"Medical Information"]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Current Medications"}),(0,t.jsx)("textarea",{value:e.medications,onChange:e=>x("medications",e.target.value),rows:3,className:"w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"List all current medications, dosages, and frequency. Write 'None' if no medications."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Allergies"}),(0,t.jsx)("textarea",{value:e.allergies,onChange:e=>x("allergies",e.target.value),rows:3,className:"w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"List all known allergies (medications, environmental, etc.). Write 'None' if no allergies."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Medical Conditions"}),(0,t.jsx)("textarea",{value:e.medicalConditions,onChange:e=>x("medicalConditions",e.target.value),rows:3,className:"w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"List any medical conditions, chronic illnesses, or ongoing health concerns. Write 'None' if no conditions."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Special Needs or Accommodations"}),(0,t.jsx)("textarea",{value:e.specialNeeds,onChange:e=>x("specialNeeds",e.target.value),rows:3,className:"w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Describe any special needs, accommodations, or assistance required. Write 'None' if no special needs."})]})]})]}),3===r&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("h3",{className:"text-xl font-semibold text-white mb-4 flex items-center",children:[(0,t.jsx)(o.A,{className:"w-6 h-6 mr-2"}),"Dietary Information"]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Dietary Restrictions"}),(0,t.jsx)("textarea",{value:e.dietaryRestrictions,onChange:e=>x("dietaryRestrictions",e.target.value),rows:3,className:"w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"List any dietary restrictions (vegetarian, vegan, kosher, halal, etc.). Write 'None' if no restrictions."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Food Allergies"}),(0,t.jsx)("textarea",{value:e.foodAllergies,onChange:e=>x("foodAllergies",e.target.value),rows:3,className:"w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"List all food allergies and severity (nuts, dairy, gluten, etc.). Write 'None' if no food allergies."})]})]})]}),4===r&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("h3",{className:"text-xl font-semibold text-white mb-4 flex items-center",children:[(0,t.jsx)(d.A,{className:"w-6 h-6 mr-2"}),"Parental Permission"]}),(0,t.jsx)("div",{className:"bg-yellow-900/20 border border-yellow-600 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("svg",{className:"w-5 h-5 text-yellow-400",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),(0,t.jsxs)("div",{className:"ml-3",children:[(0,t.jsx)("h4",{className:"text-sm font-medium text-yellow-400",children:"Important Notice"}),(0,t.jsx)("p",{className:"mt-1 text-sm text-yellow-300",children:"For participants under 18 years of age, parental or guardian permission is required."})]})]})}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("input",{type:"checkbox",id:"parentalPermission",checked:e.parentalPermission,onChange:e=>x("parentalPermission",e.target.checked),className:"w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2"}),(0,t.jsx)("label",{htmlFor:"parentalPermission",className:"ml-2 text-sm text-gray-300",children:"I give permission for my child to participate in this youth program and acknowledge that I have read and understood all terms and conditions."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Parent/Guardian Digital Signature *"}),(0,t.jsx)("input",{type:"text",required:!0,value:e.parentSignature,onChange:e=>x("parentSignature",e.target.value),className:"w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Type your full name as digital signature"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Signature Date *"}),(0,t.jsx)("input",{type:"date",required:!0,value:e.parentSignatureDate,onChange:e=>x("parentSignatureDate",e.target.value),className:"w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]})]}),(0,t.jsxs)("div",{className:"flex justify-between mt-8 pt-6 border-t border-gray-700",children:[(0,t.jsx)("button",{type:"button",onClick:()=>{r>0&&c(r-1)},disabled:0===r,className:"px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),r===g.length-1?(0,t.jsxs)("button",{type:"submit",className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-500 flex items-center",children:[(0,t.jsx)(m,{className:"w-4 h-4 mr-2"}),"Submit Registration"]}):(0,t.jsx)("button",{type:"button",onClick:()=>{r<g.length-1&&c(r+1)},className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-500",children:"Next"})]})]})})]})}},7434:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(9946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},7580:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},9946:(e,a,r)=>{"use strict";r.d(a,{A:()=>u});var t=r(2115);let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,a,r)=>r?r.toUpperCase():a.toLowerCase()),n=e=>{let a=s(e);return a.charAt(0).toUpperCase()+a.slice(1)},i=function(){for(var e=arguments.length,a=Array(e),r=0;r<e;r++)a[r]=arguments[r];return a.filter((e,a,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===a).join(" ").trim()},o=e=>{for(let a in e)if(a.startsWith("aria-")||"role"===a||"title"===a)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,t.forwardRef)((e,a)=>{let{color:r="currentColor",size:l=24,strokeWidth:s=2,absoluteStrokeWidth:n,className:c="",children:u,iconNode:m,...g}=e;return(0,t.createElement)("svg",{ref:a,...d,width:l,height:l,stroke:r,strokeWidth:n?24*Number(s)/Number(l):s,className:i("lucide",c),...!u&&!o(g)&&{"aria-hidden":"true"},...g},[...m.map(e=>{let[a,r]=e;return(0,t.createElement)(a,r)}),...Array.isArray(u)?u:[u]])}),u=(e,a)=>{let r=(0,t.forwardRef)((r,s)=>{let{className:o,...d}=r;return(0,t.createElement)(c,{ref:s,iconNode:a,className:i("lucide-".concat(l(n(e))),"lucide-".concat(e),o),...d})});return r.displayName=n(e),r}}},e=>{var a=a=>e(e.s=a);e.O(0,[874,441,684,358],()=>a(5809)),_N_E=e.O()}]);