import { TrendingUp, Globe, Calendar, AlertTriangle } from 'lucide-react';

export default function MarketOutlook() {
  return (
    <div className="p-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-white mb-2">Market Outlook</h1>
        <p className="text-gray-400">Long-term market perspectives and strategic insights.</p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Market Sentiment</h3>
            <TrendingUp className="w-6 h-6 text-green-500" />
          </div>
          <p className="text-3xl font-bold text-green-400">Optimistic</p>
          <p className="text-sm text-gray-400">Based on 12-month outlook</p>
        </div>

        <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Risk Level</h3>
            <AlertTriangle className="w-6 h-6 text-yellow-500" />
          </div>
          <p className="text-3xl font-bold text-yellow-400">Moderate</p>
          <p className="text-sm text-gray-400">Volatility expected</p>
        </div>

        <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Global Impact</h3>
            <Globe className="w-6 h-6 text-blue-500" />
          </div>
          <p className="text-3xl font-bold text-blue-400">High</p>
          <p className="text-sm text-gray-400">Interconnected markets</p>
        </div>
      </div>

      {/* Outlook Content */}
      <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
        <h2 className="text-xl font-semibold text-white mb-6">Strategic Market Outlook</h2>
        
        <div className="space-y-6">
          <div className="border-l-4 border-blue-500 pl-6">
            <h3 className="text-lg font-semibold text-white mb-2">Economic Environment</h3>
            <p className="text-gray-400">
              The current economic landscape shows signs of resilience with controlled inflation and steady employment growth. 
              Central bank policies remain accommodative while monitoring for potential overheating signals.
            </p>
          </div>

          <div className="border-l-4 border-green-500 pl-6">
            <h3 className="text-lg font-semibold text-white mb-2">Growth Opportunities</h3>
            <p className="text-gray-400">
              Technology sector continues to drive innovation with AI and renewable energy presenting significant investment opportunities. 
              Emerging markets show potential for expansion as global trade normalizes.
            </p>
          </div>

          <div className="border-l-4 border-yellow-500 pl-6">
            <h3 className="text-lg font-semibold text-white mb-2">Risk Factors</h3>
            <p className="text-gray-400">
              Geopolitical tensions and supply chain disruptions remain key concerns. 
              Interest rate volatility could impact growth-sensitive sectors in the near term.
            </p>
          </div>

          <div className="border-l-4 border-purple-500 pl-6">
            <h3 className="text-lg font-semibold text-white mb-2">Strategic Recommendations</h3>
            <p className="text-gray-400">
              Maintain diversified portfolios with emphasis on quality growth stocks. 
              Consider defensive positions in utilities and consumer staples for stability.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
