import { 
  Heart, 
  AlertTriangle, 
  Pill, 
  Activity,
  Search,
  Filter,
  Download,
  Eye,
  Edit
} from 'lucide-react';

interface MedicalRecord {
  id: number;
  participantName: string;
  age: number;
  medications: string;
  allergies: string;
  medicalConditions: string;
  specialNeeds: string;
  emergencyContact: string;
  emergencyPhone: string;
  lastUpdated: string;
  severity: 'low' | 'medium' | 'high';
}

export default function MedicalInfoPage() {
  // Mock data - in a real app, this would come from an API
  const medicalRecords: MedicalRecord[] = [
    {
      id: 1,
      participantName: '<PERSON>',
      age: 17,
      medications: 'Albuterol inhaler (as needed for asthma)',
      allergies: 'Peanuts (severe), Shellfish (moderate)',
      medicalConditions: 'Asthma',
      specialNeeds: 'Requires inhaler access during activities',
      emergencyContact: '<PERSON>',
      emergencyPhone: '(*************',
      lastUpdated: '2024-01-14',
      severity: 'high'
    },
    {
      id: 2,
      participantName: '<PERSON>',
      age: 16,
      medications: 'Insulin (Humalog) - 3 times daily',
      allergies: 'None',
      medicalConditions: 'Type 1 Diabetes',
      specialNeeds: 'Blood sugar monitoring, insulin administration, dietary considerations',
      emergencyContact: '<PERSON>',
      emergencyPhone: '(*************',
      lastUpdated: '2024-01-12',
      severity: 'high'
    },
    {
      id: 3,
      participantName: 'Emma Davis',
      age: 15,
      medications: 'None',
      allergies: 'Latex (mild)',
      medicalConditions: 'None',
      specialNeeds: 'Latex-free gloves if medical attention needed',
      emergencyContact: 'Lisa Davis',
      emergencyPhone: '(*************',
      lastUpdated: '2024-01-13',
      severity: 'low'
    },
    {
      id: 4,
      participantName: 'Jessica Wilson',
      age: 17,
      medications: 'Epipen (emergency use only)',
      allergies: 'Tree nuts (severe), Dairy (moderate)',
      medicalConditions: 'Food allergies',
      specialNeeds: 'Epipen must be available at all times, dairy-free meals',
      emergencyContact: 'Karen Wilson',
      emergencyPhone: '(*************',
      lastUpdated: '2024-01-11',
      severity: 'high'
    }
  ];

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'high':
        return <AlertTriangle className="w-4 h-4 text-red-600" />;
      case 'medium':
        return <AlertTriangle className="w-4 h-4 text-yellow-600" />;
      case 'low':
        return <Activity className="w-4 h-4 text-green-600" />;
      default:
        return <Activity className="w-4 h-4 text-gray-600" />;
    }
  };

  const highRiskCount = medicalRecords.filter(record => record.severity === 'high').length;
  const mediumRiskCount = medicalRecords.filter(record => record.severity === 'medium').length;
  const lowRiskCount = medicalRecords.filter(record => record.severity === 'low').length;

  return (
    <div className="p-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-white mb-2">Medical Information</h1>
        <p className="text-gray-400">Monitor and manage medical information for all participants</p>
      </div>

      {/* Medical Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-400">Total Records</p>
              <p className="text-2xl font-bold text-white">{medicalRecords.length}</p>
            </div>
            <Heart className="w-8 h-8 text-blue-600" />
          </div>
        </div>
        
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-400">High Risk</p>
              <p className="text-2xl font-bold text-red-400">{highRiskCount}</p>
            </div>
            <AlertTriangle className="w-8 h-8 text-red-600" />
          </div>
        </div>
        
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-400">Medium Risk</p>
              <p className="text-2xl font-bold text-yellow-400">{mediumRiskCount}</p>
            </div>
            <AlertTriangle className="w-8 h-8 text-yellow-600" />
          </div>
        </div>
        
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-400">Low Risk</p>
              <p className="text-2xl font-bold text-green-400">{lowRiskCount}</p>
            </div>
            <Activity className="w-8 h-8 text-green-600" />
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-gray-800 border border-gray-700 rounded-lg p-6 mb-8">
        <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
          <div className="flex flex-col md:flex-row gap-4 flex-1">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search by participant name..."
                className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <select className="px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="">All Risk Levels</option>
              <option value="high">High Risk</option>
              <option value="medium">Medium Risk</option>
              <option value="low">Low Risk</option>
            </select>
            
            <select className="px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="">All Conditions</option>
              <option value="allergies">Allergies</option>
              <option value="medications">Medications</option>
              <option value="chronic">Chronic Conditions</option>
            </select>
          </div>
          
          <button className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-500 transition-colors">
            <Download className="w-4 h-4 mr-2" />
            Export Medical Records
          </button>
        </div>
      </div>

      {/* Medical Records Table */}
      <div className="bg-gray-800 border border-gray-700 rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-700">
          <h3 className="text-lg font-semibold text-white">Medical Records</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Participant</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Risk Level</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Medications</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Allergies</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Conditions</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Emergency Contact</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-700">
              {medicalRecords.map((record) => (
                <tr key={record.id} className="hover:bg-gray-700">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-white">{record.participantName}</div>
                    <div className="text-sm text-gray-400">Age: {record.age}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {getSeverityIcon(record.severity)}
                      <span className={`ml-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full border ${getSeverityColor(record.severity)}`}>
                        {record.severity} risk
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-300 max-w-xs truncate" title={record.medications}>
                      {record.medications || 'None'}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-300 max-w-xs truncate" title={record.allergies}>
                      {record.allergies || 'None'}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-300 max-w-xs truncate" title={record.medicalConditions}>
                      {record.medicalConditions || 'None'}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-300">{record.emergencyContact}</div>
                    <div className="text-sm text-gray-400">{record.emergencyPhone}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button className="text-blue-400 hover:text-blue-300" title="View Full Record">
                        <Eye className="w-4 h-4" />
                      </button>
                      <button className="text-green-400 hover:text-green-300" title="Edit Record">
                        <Edit className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Emergency Procedures */}
      <div className="mt-8 bg-red-900/20 border border-red-600 rounded-lg p-6">
        <div className="flex items-start">
          <AlertTriangle className="w-6 h-6 text-red-400 mt-1" />
          <div className="ml-3">
            <h3 className="text-lg font-semibold text-red-400 mb-2">Emergency Procedures</h3>
            <div className="text-sm text-red-300 space-y-2">
              <p>• For severe allergic reactions: Administer EpiPen if available and call 911 immediately</p>
              <p>• For asthma attacks: Help participant use inhaler and monitor breathing</p>
              <p>• For diabetic emergencies: Check blood sugar if possible and contact emergency services</p>
              <p>• Always contact the participant's emergency contact after any medical incident</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
