(()=>{var e={};e.id=615,e.ids=[615],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3341:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u});var r=s(7413),a=s(4536),i=s.n(a),n=s(1382),l=s(4702),d=s(3148),o=s(6373);let c=(0,o.A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]),x=(0,o.A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]),p=(0,o.A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),m=(0,o.A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]),h=(0,o.A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);function u(){let e=e=>{switch(e){case"approved":return"bg-green-100 text-green-800 border-green-200";case"pending":return"bg-yellow-100 text-yellow-800 border-yellow-200";case"rejected":return"bg-red-100 text-red-800 border-red-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}};return(0,r.jsxs)("div",{className:"p-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"Youth Registrations"}),(0,r.jsx)("p",{className:"text-gray-400",children:"Manage and review youth program registrations"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,r.jsx)("div",{className:"bg-gray-800 border border-gray-700 rounded-lg p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-400",children:"Total"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-white",children:"1,247"})]}),(0,r.jsx)(n.A,{className:"w-8 h-8 text-blue-600"})]})}),(0,r.jsx)("div",{className:"bg-gray-800 border border-gray-700 rounded-lg p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-400",children:"Approved"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-white",children:"1,156"})]}),(0,r.jsx)(l.A,{className:"w-8 h-8 text-green-600"})]})}),(0,r.jsx)("div",{className:"bg-gray-800 border border-gray-700 rounded-lg p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-400",children:"Pending"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-white",children:"91"})]}),(0,r.jsx)(d.A,{className:"w-8 h-8 text-yellow-600"})]})}),(0,r.jsx)("div",{className:"bg-gray-800 border border-gray-700 rounded-lg p-6",children:(0,r.jsxs)(i(),{href:"/registrations/new",className:"flex items-center justify-between hover:bg-gray-700 transition-colors rounded-lg p-2 -m-2",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-400",children:"New Registration"}),(0,r.jsx)("p",{className:"text-lg font-bold text-blue-400",children:"Add New"})]}),(0,r.jsx)(c,{className:"w-8 h-8 text-blue-600"})]})})]}),(0,r.jsx)("div",{className:"bg-gray-800 border border-gray-700 rounded-lg p-6 mb-8",children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(x,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,r.jsx)("input",{type:"text",placeholder:"Search by name, email, or phone...",className:"w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"})]})}),(0,r.jsxs)("select",{className:"px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"",children:"All Status"}),(0,r.jsx)("option",{value:"approved",children:"Approved"}),(0,r.jsx)("option",{value:"pending",children:"Pending"}),(0,r.jsx)("option",{value:"rejected",children:"Rejected"})]}),(0,r.jsxs)("select",{className:"px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"",children:"All Ages"}),(0,r.jsx)("option",{value:"13-15",children:"13-15 years"}),(0,r.jsx)("option",{value:"16-18",children:"16-18 years"}),(0,r.jsx)("option",{value:"19+",children:"19+ years"})]})]})}),(0,r.jsxs)("div",{className:"bg-gray-800 border border-gray-700 rounded-lg overflow-hidden",children:[(0,r.jsx)("div",{className:"px-6 py-4 border-b border-gray-700",children:(0,r.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Recent Registrations"})}),(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full",children:[(0,r.jsx)("thead",{className:"bg-gray-700",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Name"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Age"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Contact"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Status"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Alerts"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Date"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"divide-y divide-gray-700",children:[{id:1,name:"Sarah Johnson",age:16,email:"<EMAIL>",phone:"(*************",status:"approved",registrationDate:"2024-01-15",emergencyContact:"Mary Johnson",medicalAlerts:!1,dietaryRestrictions:!0},{id:2,name:"Alex Chen",age:17,email:"<EMAIL>",phone:"(*************",status:"pending",registrationDate:"2024-01-14",emergencyContact:"David Chen",medicalAlerts:!0,dietaryRestrictions:!1},{id:3,name:"Emma Davis",age:15,email:"<EMAIL>",phone:"(*************",status:"approved",registrationDate:"2024-01-13",emergencyContact:"Lisa Davis",medicalAlerts:!1,dietaryRestrictions:!1},{id:4,name:"Michael Brown",age:16,email:"<EMAIL>",phone:"(*************",status:"pending",registrationDate:"2024-01-12",emergencyContact:"Robert Brown",medicalAlerts:!0,dietaryRestrictions:!0}].map(t=>(0,r.jsxs)("tr",{className:"hover:bg-gray-700",children:[(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-white",children:t.name}),(0,r.jsx)("div",{className:"text-sm text-gray-400",children:t.email})]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-300",children:t.age}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsx)("div",{className:"text-sm text-gray-300",children:t.phone}),(0,r.jsxs)("div",{className:"text-sm text-gray-400",children:["Emergency: ",t.emergencyContact]})]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full border ${e(t.status)}`,children:t.status})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-300",children:(0,r.jsxs)("div",{className:"flex space-x-2",children:[t.medicalAlerts&&(0,r.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800 border border-red-200",children:"Medical"}),t.dietaryRestrictions&&(0,r.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800 border border-orange-200",children:"Dietary"})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-300",children:new Date(t.registrationDate).toLocaleDateString()}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{className:"text-blue-400 hover:text-blue-300",children:(0,r.jsx)(p,{className:"w-4 h-4"})}),(0,r.jsx)("button",{className:"text-green-400 hover:text-green-300",children:(0,r.jsx)(m,{className:"w-4 h-4"})}),(0,r.jsx)("button",{className:"text-red-400 hover:text-red-300",children:(0,r.jsx)(h,{className:"w-4 h-4"})})]})})]},t.id))})]})})]})]})}},3873:e=>{"use strict";e.exports=require("path")},4013:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,5814,23))},4536:(e,t,s)=>{let{createProxy:r}=s(9844);e.exports=r("C:\\Users\\<USER>\\Desktop\\youth system\\youth-dashboard\\node_modules\\next\\dist\\client\\app-dir\\link.js")},6262:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>p,tree:()=>o});var r=s(5239),a=s(8088),i=s(8170),n=s.n(i),l=s(893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(t,d);let o={children:["",{children:["registrations",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,3341)),"C:\\Users\\<USER>\\Desktop\\youth system\\youth-dashboard\\src\\app\\registrations\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"C:\\Users\\<USER>\\Desktop\\youth system\\youth-dashboard\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\youth system\\youth-dashboard\\src\\app\\registrations\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/registrations/page",pathname:"/registrations",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},9101:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,4536,23))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,354,658,609],()=>s(6262));module.exports=r})();