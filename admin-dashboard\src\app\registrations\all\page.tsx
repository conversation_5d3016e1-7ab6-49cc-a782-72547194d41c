'use client';

import { useState } from 'react';
import { 
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Trash2,
  User<PERSON>he<PERSON>,
  User<PERSON>,
  Clock,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';

interface Registration {
  id: number;
  name: string;
  age: number;
  email: string;
  phone: string;
  status: 'approved' | 'pending' | 'rejected';
  registrationDate: string;
  emergencyContact: string;
  medicalAlerts: boolean;
  dietaryRestrictions: boolean;
  parentGuardian: string;
  address: string;
}

export default function AllRegistrationsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [ageFilter, setAgeFilter] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  // Mock data - in a real app, this would come from an API
  const allRegistrations: Registration[] = [
    {
      id: 1,
      name: '<PERSON>',
      age: 16,
      email: '<EMAIL>',
      phone: '(*************',
      status: 'approved',
      registrationDate: '2024-01-15',
      emergencyContact: '<PERSON>',
      medicalAlerts: false,
      dietaryRestrictions: true,
      parentGuardian: '<PERSON>',
      address: '123 Main St, Anytown, ST 12345'
    },
    {
      id: 2,
      name: 'Alex Chen',
      age: 17,
      email: '<EMAIL>',
      phone: '(*************',
      status: 'pending',
      registrationDate: '2024-01-14',
      emergencyContact: 'David Chen',
      medicalAlerts: true,
      dietaryRestrictions: false,
      parentGuardian: 'David Chen',
      address: '456 Oak Ave, Somewhere, ST 23456'
    },
    {
      id: 3,
      name: 'Emma Davis',
      age: 15,
      email: '<EMAIL>',
      phone: '(*************',
      status: 'approved',
      registrationDate: '2024-01-13',
      emergencyContact: 'Lisa Davis',
      medicalAlerts: false,
      dietaryRestrictions: false,
      parentGuardian: 'Lisa Davis',
      address: '789 Pine Rd, Elsewhere, ST 34567'
    },
    {
      id: 4,
      name: 'Michael Brown',
      age: 16,
      email: '<EMAIL>',
      phone: '(*************',
      status: 'pending',
      registrationDate: '2024-01-12',
      emergencyContact: 'Robert Brown',
      medicalAlerts: true,
      dietaryRestrictions: true,
      parentGuardian: 'Robert Brown',
      address: '321 Elm St, Nowhere, ST 45678'
    },
    {
      id: 5,
      name: 'Jessica Wilson',
      age: 17,
      email: '<EMAIL>',
      phone: '(*************',
      status: 'approved',
      registrationDate: '2024-01-11',
      emergencyContact: 'Karen Wilson',
      medicalAlerts: false,
      dietaryRestrictions: true,
      parentGuardian: 'Karen Wilson',
      address: '654 Maple Dr, Anywhere, ST 56789'
    },
    {
      id: 6,
      name: 'Ryan Martinez',
      age: 15,
      email: '<EMAIL>',
      phone: '(*************',
      status: 'rejected',
      registrationDate: '2024-01-10',
      emergencyContact: 'Carlos Martinez',
      medicalAlerts: false,
      dietaryRestrictions: false,
      parentGuardian: 'Carlos Martinez',
      address: '987 Cedar Ln, Someplace, ST 67890'
    }
  ];

  // Filter registrations based on search and filters
  const filteredRegistrations = allRegistrations.filter(registration => {
    const matchesSearch = registration.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         registration.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         registration.phone.includes(searchTerm);
    
    const matchesStatus = statusFilter === '' || registration.status === statusFilter;
    
    const matchesAge = ageFilter === '' || 
                      (ageFilter === '13-15' && registration.age >= 13 && registration.age <= 15) ||
                      (ageFilter === '16-18' && registration.age >= 16 && registration.age <= 18) ||
                      (ageFilter === '19+' && registration.age >= 19);
    
    return matchesSearch && matchesStatus && matchesAge;
  });

  // Pagination
  const totalPages = Math.ceil(filteredRegistrations.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentRegistrations = filteredRegistrations.slice(startIndex, endIndex);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'rejected':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const handleStatusChange = (id: number, newStatus: 'approved' | 'rejected') => {
    // In a real app, this would make an API call
    console.log(`Changing status of registration ${id} to ${newStatus}`);
    alert(`Registration ${id} status changed to ${newStatus}`);
  };

  const handleExport = () => {
    // In a real app, this would generate and download a CSV/Excel file
    console.log('Exporting registrations...');
    alert('Export functionality would be implemented here');
  };

  return (
    <div className="p-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-white mb-2">All Registrations</h1>
        <p className="text-gray-400">View and manage all youth program registrations</p>
      </div>

      {/* Filters and Search */}
      <div className="bg-gray-800 border border-gray-700 rounded-lg p-6 mb-8">
        <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
          <div className="flex flex-col md:flex-row gap-4 flex-1">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search by name, email, or phone..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Status</option>
              <option value="approved">Approved</option>
              <option value="pending">Pending</option>
              <option value="rejected">Rejected</option>
            </select>
            
            <select
              value={ageFilter}
              onChange={(e) => setAgeFilter(e.target.value)}
              className="px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Ages</option>
              <option value="13-15">13-15 years</option>
              <option value="16-18">16-18 years</option>
              <option value="19+">19+ years</option>
            </select>
          </div>
          
          <button
            onClick={handleExport}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-500 transition-colors"
          >
            <Download className="w-4 h-4 mr-2" />
            Export
          </button>
        </div>
      </div>

      {/* Results Summary */}
      <div className="mb-6">
        <p className="text-gray-400">
          Showing {startIndex + 1}-{Math.min(endIndex, filteredRegistrations.length)} of {filteredRegistrations.length} registrations
        </p>
      </div>

      {/* Registrations Table */}
      <div className="bg-gray-800 border border-gray-700 rounded-lg overflow-hidden mb-8">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Participant</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Age</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Contact</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Alerts</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-700">
              {currentRegistrations.map((registration) => (
                <tr key={registration.id} className="hover:bg-gray-700">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-white">{registration.name}</div>
                    <div className="text-sm text-gray-400">{registration.email}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                    {registration.age}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-300">{registration.phone}</div>
                    <div className="text-sm text-gray-400">Emergency: {registration.emergencyContact}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full border ${getStatusColor(registration.status)}`}>
                      {registration.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                    <div className="flex space-x-2">
                      {registration.medicalAlerts && (
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800 border border-red-200">
                          Medical
                        </span>
                      )}
                      {registration.dietaryRestrictions && (
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800 border border-orange-200">
                          Dietary
                        </span>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                    {new Date(registration.registrationDate).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button className="text-blue-400 hover:text-blue-300" title="View Details">
                        <Eye className="w-4 h-4" />
                      </button>
                      <button className="text-green-400 hover:text-green-300" title="Edit">
                        <Edit className="w-4 h-4" />
                      </button>
                      {registration.status === 'pending' && (
                        <>
                          <button 
                            onClick={() => handleStatusChange(registration.id, 'approved')}
                            className="text-green-400 hover:text-green-300" 
                            title="Approve"
                          >
                            <UserCheck className="w-4 h-4" />
                          </button>
                          <button 
                            onClick={() => handleStatusChange(registration.id, 'rejected')}
                            className="text-red-400 hover:text-red-300" 
                            title="Reject"
                          >
                            <UserX className="w-4 h-4" />
                          </button>
                        </>
                      )}
                      <button className="text-red-400 hover:text-red-300" title="Delete">
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-400">
            Page {currentPage} of {totalPages}
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className="flex items-center px-3 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronLeft className="w-4 h-4 mr-1" />
              Previous
            </button>
            <button
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
              className="flex items-center px-3 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
              <ChevronRight className="w-4 h-4 ml-1" />
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
