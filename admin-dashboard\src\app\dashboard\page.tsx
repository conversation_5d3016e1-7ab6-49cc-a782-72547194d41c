import {
  BarChart3,
  TrendingUp,
  Users,
  UserCheck,
  Clock,
  ArrowUpRight,
  ArrowDownRight,
  Heart,
  Activity
} from 'lucide-react';

export default function Dashboard() {
  const stats = [
    {
      title: 'Total Registrations',
      value: '1,247',
      change: '+12.5%',
      trend: 'up',
      icon: Users,
    },
    {
      title: 'Approved',
      value: '1,156',
      change: '+8.2%',
      trend: 'up',
      icon: UserCheck,
    },
    {
      title: 'Pending Review',
      value: '91',
      change: '+15',
      trend: 'up',
      icon: Clock,
    },
    {
      title: 'Medical Alerts',
      value: '23',
      change: '+3',
      trend: 'up',
      icon: Heart,
    },
  ];

  return (
    <div className="p-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-white mb-2">Dashboard</h1>
        <p className="text-gray-400">Welcome back! Here's an overview of your youth registration system.</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {stats.map((stat) => (
          <div key={stat.title} className="bg-gray-800 border border-gray-700 rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-400">{stat.title}</p>
                <p className="text-2xl font-bold text-white">{stat.value}</p>
              </div>
              <div className="w-12 h-12 bg-blue-600/10 rounded-lg flex items-center justify-center">
                <stat.icon className="w-6 h-6 text-blue-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center">
              {stat.trend === 'up' ? (
                <ArrowUpRight className="w-4 h-4 text-green-500 mr-1" />
              ) : (
                <ArrowDownRight className="w-4 h-4 text-red-500 mr-1" />
              )}
              <span className={`text-sm font-medium ${
                stat.trend === 'up' ? 'text-green-500' : 'text-red-500'
              }`}>
                {stat.change}
              </span>
              <span className="text-sm text-gray-400 ml-1">from last month</span>
            </div>
          </div>
        ))}
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Registration Trends Chart */}
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Registration Trends</h3>
            <TrendingUp className="w-5 h-5 text-blue-600" />
          </div>
          <div className="h-64 flex items-center justify-center text-gray-400">
            <div className="text-center">
              <BarChart3 className="w-16 h-16 mx-auto mb-4 text-gray-600" />
              <p>Registration trends chart would go here</p>
              <p className="text-sm">Total Registrations: 1,247</p>
            </div>
          </div>
        </div>

        {/* Age Distribution Chart */}
        <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Age Distribution</h3>
            <Activity className="w-5 h-5 text-green-600" />
          </div>
          <div className="h-64 flex items-center justify-center text-gray-400">
            <div className="text-center">
              <Activity className="w-16 h-16 mx-auto mb-4 text-gray-600" />
              <p>Age distribution chart would go here</p>
              <p className="text-sm">Average Age: 16.2 years</p>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Recent Registrations</h3>
        <div className="space-y-4">
          {[
            { action: 'Sarah Johnson registered', time: '2 minutes ago', type: 'registration' },
            { action: 'Medical form submitted by Alex Chen', time: '5 minutes ago', type: 'medical' },
            { action: 'Emergency contact updated for Emma Davis', time: '10 minutes ago', type: 'update' },
            { action: 'Registration approved for Michael Brown', time: '1 hour ago', type: 'approval' },
          ].map((activity, index) => (
            <div key={index} className="flex items-center justify-between py-3 border-b border-gray-700 last:border-b-0">
              <div className="flex items-center space-x-3">
                <div className={`w-2 h-2 rounded-full ${
                  activity.type === 'registration' ? 'bg-blue-500' :
                  activity.type === 'medical' ? 'bg-red-500' :
                  activity.type === 'update' ? 'bg-yellow-500' :
                  'bg-green-500'
                }`} />
                <span className="text-white">{activity.action}</span>
              </div>
              <span className="text-sm text-gray-400">{activity.time}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
